import $api from '@/api/index.js'

/**
 * APP热更新工具类
 * 支持强制更新和静默更新
 */
class AppUpdate {
  constructor() {
    this.isChecking = false // 防止重复检查
    this.currentVersion = '' // 当前版本号
    this.platform = 2 // 平台类型：1-师傅端，2-用户端
  }

  /**
   * 获取当前APP版本信息
   */
  getCurrentVersion() {
    // #ifdef APP-PLUS
    try {
      const systemInfo = uni.getSystemInfoSync()
      this.currentVersion = systemInfo.appVersion || '1.0.0'
      return this.currentVersion
    } catch (error) {
      console.error('获取版本信息失败:', error)
      this.currentVersion = '1.0.0'
      return this.currentVersion
    }
    // #endif

    // #ifndef APP-PLUS
    // 非APP环境返回默认版本
    this.currentVersion = '1.0.0'
    return this.currentVersion
    // #endif
  }

  /**
   * 检查版本更新
   * @param {Object} options 配置选项
   * @param {Boolean} options.silent 是否静默检查（不显示"已是最新版本"提示）
   * @param {Boolean} options.showLoading 是否显示加载提示
   */
  async checkUpdate(options = {}) {
    const { silent = false, showLoading = true } = options
    
    // 防止重复检查
    if (this.isChecking) {
      console.log('正在检查更新中...')
      return
    }

    // #ifndef APP-PLUS
    if (!silent) {
      uni.showToast({
        title: '仅支持APP版本更新',
        icon: 'none'
      })
    }
    return
    // #endif

    // #ifdef APP-PLUS
    this.isChecking = true
    
    try {
      if (showLoading) {
        uni.showLoading({
          title: '检查更新中...'
        })
      }

      // 获取当前版本
      const currentVersion = this.getCurrentVersion()
      
      // 调用后端接口检查更新
      const response = await $api.user.checkAppVersion({
        version: currentVersion,
        platform: this.platform
      })

      if (showLoading) {
        uni.hideLoading()
      }

      if (response.code === '200' && response.data) {
        const updateInfo = response.data
        
        if (updateInfo.needUpdate) {
          // 需要更新
          this.showUpdateDialog(updateInfo)
        } else {
          // 已是最新版本
          if (!silent) {
            uni.showToast({
              title: '已是最新版本',
              icon: 'success'
            })
          }
        }
      } else {
        throw new Error(response.msg || '检查更新失败')
      }
    } catch (error) {
      console.error('检查更新失败:', error)
      if (showLoading) {
        uni.hideLoading()
      }
      if (!silent) {
        uni.showToast({
          title: '检查更新失败',
          icon: 'none'
        })
      }
    } finally {
      this.isChecking = false
    }
    // #endif
  }

  /**
   * 显示更新对话框
   * @param {Object} updateInfo 更新信息
   */
  showUpdateDialog(updateInfo) {
    const {
      latestVersion,
      description,
      wgtUrl,
      forceUpdate
    } = updateInfo

    const title = `发现新版本 v${latestVersion}`
    const content = description || '建议您立即更新以获得更好的使用体验'
    
    uni.showModal({
      title,
      content,
      showCancel: !forceUpdate, // 强制更新时不显示取消按钮
      confirmText: '立即更新',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          this.downloadAndInstall(updateInfo)
        } else if (forceUpdate) {
          // 强制更新时，用户点击取消也要更新
          this.downloadAndInstall(updateInfo)
        }
      }
    })
  }

  /**
   * 下载并安装更新包
   * @param {Object} updateInfo 更新信息
   */
  downloadAndInstall(updateInfo) {
    const { wgtUrl, forceUpdate } = updateInfo
    
    if (!wgtUrl) {
      uni.showToast({
        title: '下载地址无效',
        icon: 'none'
      })
      return
    }

    // 显示下载进度
    uni.showLoading({
      title: '正在下载...'
    })

    // 下载更新包
    const downloadTask = uni.downloadFile({
      url: wgtUrl,
      success: (res) => {
        uni.hideLoading()
        
        if (res.statusCode === 200) {
          this.installUpdate(res.tempFilePath, forceUpdate)
        } else {
          uni.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
      },
      fail: (error) => {
        uni.hideLoading()
        console.error('下载失败:', error)
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    })

    // 监听下载进度
    downloadTask.onProgressUpdate((res) => {
      const progress = Math.round(res.progress)
      uni.showLoading({
        title: `下载中 ${progress}%`
      })
    })
  }

  /**
   * 安装更新包
   * @param {String} filePath 文件路径
   * @param {Boolean} forceUpdate 是否强制更新
   */
  installUpdate(filePath, forceUpdate = false) {
    // #ifdef APP-PLUS
    plus.runtime.install(filePath, {
      force: false
    }, () => {
      // 安装成功
      const message = forceUpdate ? '更新完成，应用将重启' : '更新完成，是否重启应用？'
      
      if (forceUpdate) {
        uni.showToast({
          title: message,
          icon: 'success',
          duration: 2000
        })
        
        setTimeout(() => {
          plus.runtime.restart()
        }, 2000)
      } else {
        uni.showModal({
          title: '安装成功',
          content: message,
          showCancel: true,
          confirmText: '立即重启',
          cancelText: '稍后重启',
          success: (res) => {
            if (res.confirm) {
              plus.runtime.restart()
            }
          }
        })
      }
    }, (error) => {
      // 安装失败
      console.error('安装失败:', error)
      uni.showModal({
        title: '安装失败',
        content: error.message || '安装过程中出现错误',
        showCancel: false
      })
    })
    // #endif
  }

  /**
   * 静默更新（后台下载，下次启动生效）
   * @param {Object} updateInfo 更新信息
   */
  silentUpdate(updateInfo) {
    const { wgtUrl } = updateInfo
    
    if (!wgtUrl) {
      console.error('静默更新失败: 下载地址无效')
      return
    }

    console.log('开始静默更新...')
    
    uni.downloadFile({
      url: wgtUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          // #ifdef APP-PLUS
          plus.runtime.install(res.tempFilePath, {
            force: false
          }, () => {
            console.log('静默更新安装成功，下次启动生效')
          }, (error) => {
            console.error('静默更新安装失败:', error)
          })
          // #endif
        }
      },
      fail: (error) => {
        console.error('静默更新下载失败:', error)
      }
    })
  }
}

// 创建单例实例
const appUpdate = new AppUpdate()

export default appUpdate

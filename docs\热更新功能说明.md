# 今师傅APP热更新功能说明

## 功能概述

本项目已集成APP热更新功能，支持自动检查版本更新、下载安装包、静默更新等特性。热更新功能基于自定义后端接口实现，支持强制更新和可选更新两种模式。

## 功能特点

- ✅ 自动版本检查（APP启动时）
- ✅ 手动检查更新（设置页面）
- ✅ 强制更新支持
- ✅ 静默更新支持
- ✅ 下载进度显示
- ✅ 安装完成后重启提示
- ✅ 友好的用户界面

## 技术实现

### 1. 后端接口

**接口地址**: `/api/user/login/checkAppVersion`

**请求参数**:
```json
{
    "version": "*******",  // 当前版本号
    "platform": 2         // 平台类型：1-师傅端，2-用户端
}
```

**返回数据**:
```json
{
    "code": "200",
    "msg": "",
    "data": {
        "needUpdate": true,           // 是否需要更新
        "latestVersion": "1.0.3",     // 最新版本号
        "wgtUrl": "https://example.com/app_v1.0.3.wgt", // 下载地址
        "description": "修复部分已知问题2", // 更新说明
        "forceUpdate": 1,             // 是否强制更新：1-强制，0-可选
        "platform": 2                // 平台类型
    }
}
```

### 2. 核心文件

#### utils/app-update.js
热更新工具类，包含以下主要方法：
- `checkUpdate()` - 检查版本更新
- `downloadAndInstall()` - 下载并安装更新包
- `silentUpdate()` - 静默更新
- `getCurrentVersion()` - 获取当前版本号

#### components/app-update-check.vue
更新检查组件，可在任意页面中使用

#### pages/app-update.vue
专门的更新页面，提供完整的更新体验

### 3. 集成方式

#### 在App.vue中自动检查
```javascript
// App.vue onLaunch方法中
import appUpdate from "@/utils/app-update.js"

// 延迟检查更新，避免影响启动速度
setTimeout(() => {
    appUpdate.checkUpdate({ silent: true, showLoading: false })
}, 3000)
```

#### 在设置页面添加手动检查
```javascript
// 在任意页面中使用
import appUpdate from "@/utils/app-update.js"

// 手动检查更新
checkUpdate() {
    appUpdate.checkUpdate({
        silent: false,    // 显示"已是最新版本"提示
        showLoading: true // 显示加载提示
    })
}
```

## 使用说明

### 1. 自动检查更新

APP启动后会自动在后台检查更新（延迟3秒），如果发现新版本会弹出更新提示。

### 2. 手动检查更新

用户可以在"我的"页面点击"检查更新"进入更新页面，手动检查并下载更新。

### 3. 强制更新

当后端返回`forceUpdate: 1`时，用户必须更新才能继续使用APP。

### 4. 静默更新

支持后台静默下载更新包，下次启动时自动生效（需要后端配置支持）。

## 配置说明

### 1. 版本号配置

在`manifest.json`中配置当前版本：
```json
{
    "versionName": "1.1.2",  // 版本名称
    "versionCode": 112       // 版本代码
}
```

### 2. 平台类型配置

在`utils/app-update.js`中配置平台类型：
```javascript
this.platform = 2 // 1-师傅端，2-用户端
```

### 3. API接口配置

在`api/modules/user.js`中已添加版本检查接口：
```javascript
checkAppVersion(param) {
    return req.post("user/login/checkAppVersion", param)
}
```

## 注意事项

1. **仅支持APP环境**: 热更新功能仅在APP环境下生效，H5和小程序环境会显示相应提示。

2. **网络权限**: 确保APP有网络访问权限，用于下载更新包。

3. **存储权限**: 确保APP有存储权限，用于保存下载的更新包。

4. **版本号格式**: 建议使用语义化版本号格式，如`1.0.0`。

5. **更新包大小**: 建议控制更新包大小，避免用户流量消耗过大。

## 测试建议

1. **版本检查测试**: 修改当前版本号，测试版本检查逻辑。

2. **强制更新测试**: 后端返回强制更新标识，测试强制更新流程。

3. **网络异常测试**: 断网情况下测试错误处理。

4. **下载中断测试**: 下载过程中断网，测试重试机制。

## 常见问题

### Q: 为什么检查更新失败？
A: 请检查网络连接和后端接口是否正常。

### Q: 下载的更新包在哪里？
A: 更新包下载到临时目录，安装后会自动清理。

### Q: 如何跳过某个版本的更新？
A: 目前不支持跳过版本，建议后端控制更新策略。

### Q: 静默更新何时生效？
A: 静默更新在下次APP启动时生效。

## 更新日志

- v1.0.0: 初始版本，支持基本的版本检查和更新功能
- v1.1.0: 添加强制更新和静默更新支持
- v1.2.0: 优化用户界面，添加下载进度显示

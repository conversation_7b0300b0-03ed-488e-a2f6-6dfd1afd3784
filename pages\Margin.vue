<template>
	<view class="page">
		<view class="box">
			<view class="money">{{money}}</view>
			<view class="title">保证金金额（元）</view>
			<view class="btn" @click="submit">缴纳保证金</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				money:''
			}
		},
		methods: {
			// 检查当前平台
			getCurrentPlatform() {
				// #ifdef APP-PLUS
				return 'app-plus';
				// #endif
				// #ifdef MP-WEIXIN
				return 'mp-weixin';
				// #endif
				// #ifdef H5
				return 'h5';
				// #endif
				return 'unknown';
			},

			// APP微信支付处理
			handleAppWechatPay(obj) {
				console.log(111)
				uni.requestPayment({
					"provider": "wxpay",
					    orderInfo: 'orderInfo',
					orderInfo: {
						appid: obj.appId,
						noncestr: obj.nonceStr,
						package: 'Sign=WXPay',
						partnerid: obj.partnerId,
						prepayid: obj.prepayId,
						timestamp: String(obj.timestamp),
						sign: obj.sign
					},
					success: (res) => {
						console.log('APP微信支付成功', res);
						uni.showToast({
							title: '支付成功',
							icon: 'success',
							duration: 1500
						})
						this.getMoney()
					},
					fail: (err) => {
						console.error('APP微信支付失败:', err);
						if (err.errMsg && err.errMsg.includes('cancel')) {
							uni.showToast({
								title: '您已取消支付',
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: '支付失败，请稍后重试',
								icon: 'none'
							});
						}
					}
				});
			},

			// 微信小程序支付处理（保持原有逻辑）
			handleMiniProgramPay(obj) {
				uni.requestPayment({
					provider: 'wxpay',
					timeStamp: obj.timeStamp,
					nonceStr: obj.nonceStr,
					package: obj.package,
					signType: obj.signType,
					paySign: obj.paySign,
					success: (res1) => {
						// 支付成功回调
						uni.showToast({
							title: '支付成功',
							icon: 'success',
							duration: 1500
						})
						this.getMoney()
					},
					fail: (err) => {
						// 支付失败回调
						uni.showToast({
							title: '支付失败请检查网络',
							icon: 'error'
						})
					},
				})
			},
			getMoney(){
				this.$api.service.seeBzj().then(res=>{
					this.money = res.money
				})
			},
			submit(){
				if(this.money == 0 || this.money == ''){
					this.$api.service.masterPayY().then(res=>{
						let obj = res.pay_list

						// 获取当前平台
						const platform = this.getCurrentPlatform();
						console.log('当前平台:', platform);

						// 根据平台选择不同的支付方式
						if (platform === 'app-plus') {
							// APP环境使用微信支付
							console.log('APP环境，使用微信支付');
							this.handleAppWechatPay(obj);
						} else if (platform === 'mp-weixin') {
							// 微信小程序环境保持原有逻辑
							console.log('微信小程序环境，使用小程序支付');
							this.handleMiniProgramPay(obj);
						} else {
							// 其他环境（H5等）
							console.log('其他环境，使用默认支付方式');
							this.handleMiniProgramPay(obj);
						}
					})
				}else{
					uni.showToast({
						icon:'none',
						title:'您已缴纳保证金快去接单吧'
					})
				}
			}
		},
		onLoad() {
			this.getMoney()
		}
	}
</script>

<style scoped lang="scss">
.page{
	background: #F8F8F8;
	height: 100vh;
	.box{
		padding: 50rpx 82rpx;
		background: #fff;
		.money{
			margin: 0 auto;
			width: fit-content;
			font-size: 80rpx;
			font-weight: 500;
			color: #171717;
		}
		.title{
			margin: 0 auto;
			margin-top: 20rpx;
			width: fit-content;
			font-size: 24rpx;
			font-weight: 400;
			color: #171717;
		}
		.btn{
			margin: 0 auto;
			margin-top: 64rpx;
			width: 584rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			line-height: 98rpx;
			text-align: center;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
		}
	}
}
</style>

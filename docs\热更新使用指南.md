# 今师傅APP热更新使用指南

## 快速开始

### 1. 基本集成

热更新功能已经集成到项目中，主要包含以下文件：

```
utils/app-update.js          # 热更新核心工具类
api/modules/user.js          # 添加了版本检查API
components/app-update-check.vue  # 更新检查组件
pages/app-update.vue         # 专门的更新页面
pages/update-test.vue        # 测试页面（可选）
```

### 2. 自动检查更新

APP启动时会自动检查更新，相关代码在 `App.vue` 中：

```javascript
// App.vue onLaunch方法中已添加
setTimeout(() => {
    appUpdate.checkUpdate({ silent: true, showLoading: false })
}, 3000) // 延迟3秒检查
```

### 3. 手动检查更新

在"我的"页面已添加"检查更新"入口，用户可以手动检查更新。

## 使用方法

### 方法一：直接调用工具类

```javascript
import appUpdate from '@/utils/app-update.js'

// 检查更新（显示提示）
appUpdate.checkUpdate({
    silent: false,    // 显示"已是最新版本"提示
    showLoading: true // 显示加载提示
})

// 静默检查（不显示提示）
appUpdate.checkUpdate({
    silent: true,
    showLoading: false
})

// 获取当前版本
const version = appUpdate.getCurrentVersion()
```

### 方法二：使用更新组件

```vue
<template>
  <view>
    <!-- 在任意页面中使用更新检查组件 -->
    <app-update-check></app-update-check>
  </view>
</template>

<script>
import AppUpdateCheck from '@/components/app-update-check.vue'

export default {
  components: {
    AppUpdateCheck
  }
}
</script>
```

### 方法三：跳转到更新页面

```javascript
// 跳转到专门的更新页面
uni.navigateTo({
    url: '/pages/app-update'
})
```

## 后端接口配置

### 接口地址
```
POST /api/user/login/checkAppVersion
```

### 请求参数
```json
{
    "version": "*******",  // 当前版本号
    "platform": 2         // 平台类型：1-师傅端，2-用户端
}
```

### 返回格式
```json
{
    "code": "200",
    "msg": "",
    "data": {
        "needUpdate": true,                    // 是否需要更新
        "latestVersion": "1.0.3",             // 最新版本号
        "wgtUrl": "https://example.com/app_v1.0.3.wgt", // 下载地址
        "description": "修复部分已知问题2",      // 更新说明
        "forceUpdate": 1,                     // 是否强制更新：1-强制，0-可选
        "platform": 2                        // 平台类型
    }
}
```

## 配置说明

### 1. 修改平台类型

在 `utils/app-update.js` 中修改：

```javascript
constructor() {
    this.platform = 2 // 1-师傅端，2-用户端
}
```

### 2. 修改当前版本号

在 `manifest.json` 中修改：

```json
{
    "versionName": "1.1.2",  // 显示的版本名称
    "versionCode": 112       // 版本代码（数字）
}
```

### 3. 自定义更新提示

可以修改 `utils/app-update.js` 中的 `showUpdateDialog` 方法来自定义更新提示界面。

## 测试方法

### 1. 使用测试页面

访问 `/pages/update-test` 页面，可以测试各种更新场景：

- 测试版本检查
- 测试静默检查  
- 模拟强制更新
- 模拟可选更新

### 2. 修改版本号测试

1. 在 `manifest.json` 中将版本号改为较低版本
2. 确保后端有更高版本的配置
3. 重新编译APP并测试

### 3. 模拟不同场景

```javascript
// 模拟强制更新
const mockUpdateInfo = {
    latestVersion: '1.2.0',
    description: '重要安全更新，必须立即更新',
    wgtUrl: 'https://example.com/update.wgt',
    forceUpdate: 1  // 强制更新
}

// 模拟可选更新
const mockUpdateInfo = {
    latestVersion: '1.1.5',
    description: '优化用户体验，建议更新',
    wgtUrl: 'https://example.com/update.wgt',
    forceUpdate: 0  // 可选更新
}

appUpdate.showUpdateDialog(mockUpdateInfo)
```

## 常见问题解决

### Q1: 检查更新时提示网络错误
**解决方案:**
1. 检查网络连接
2. 确认后端接口地址正确
3. 检查API接口是否正常返回数据

### Q2: 下载更新包失败
**解决方案:**
1. 检查下载链接是否有效
2. 确认APP有网络和存储权限
3. 检查更新包文件是否存在

### Q3: 安装更新包失败
**解决方案:**
1. 确认更新包格式正确（.wgt文件）
2. 检查APP签名是否一致
3. 确认设备存储空间充足

### Q4: 版本检查逻辑不正确
**解决方案:**
1. 检查版本号格式是否一致
2. 确认后端版本比较逻辑
3. 验证平台类型参数是否正确

## 最佳实践

### 1. 版本号管理
- 使用语义化版本号（如：1.0.0）
- 保持版本号格式一致
- 及时更新manifest.json中的版本信息

### 2. 更新策略
- 重要安全更新使用强制更新
- 功能优化使用可选更新
- 控制更新频率，避免频繁打扰用户

### 3. 用户体验
- 在合适的时机检查更新（如APP启动后）
- 提供清晰的更新说明
- 显示下载进度，提升用户体验

### 4. 错误处理
- 妥善处理网络异常
- 提供重试机制
- 记录错误日志便于调试

## 注意事项

1. **仅APP环境支持**: 热更新功能仅在APP环境下有效
2. **权限要求**: 需要网络和存储权限
3. **包大小控制**: 建议控制更新包大小，节省用户流量
4. **测试充分**: 发布前充分测试各种更新场景
5. **回滚准备**: 准备回滚方案，以防更新出现问题

import Build from 'android.os.Build';
import Context from 'android.content.Context';
import NotificationManager from 'android.app.NotificationManager';
import NotificationChannel from 'android.app.NotificationChannel';
import Notification from 'android.app.Notification';
import Intent from 'android.content.Intent';
import ComponentName from 'android.content.ComponentName';
import PendingIntent from 'android.app.PendingIntent';
import { CreateNotificationProgressOptions, FinishNotificationProgressOptions } from '../interface.uts';
import { ACTION_DOWNLOAD_FINISH, ACTION_DOWNLOAD_PROGRESS } from "./constant.uts"

import { setGlobalNotificationProgressCallBack, setGlobalNotificationProgressFinishCallBack } from './callbacks.uts';

export { TransparentActivity } from './TransparentActivity.uts';


const DOWNLOAD_PROGRESS_NOTIFICATION_ID : Int = 7890
const DC_DOWNLOAD_CHANNEL_ID = "下载文件"
const DC_DOWNLOAD_CHANNEL_NAME = "用于显示现在进度的渠道"


let notificationBuilder : Notification.Builder | null = null

let timeId = -1

let histroyProgress = 0

let isProgress = false



export function createNotificationProgress(options : CreateNotificationProgressOptions) : void {
	const { content, progress, onClick } = options

	if (progress == 100) {
		clearTimeout(timeId)
		const context = UTSAndroid.getAppContext() as Context
		realCreateNotificationProgress(options.title ?? getAppName(context), content, progress, onClick)
		reset()
		return
	}

	histroyProgress = progress
	if (timeId != -1) {
		return
	}

	const context = UTSAndroid.getAppContext() as Context
	if (!isProgress) {
		realCreateNotificationProgress(options.title ?? getAppName(context), content, histroyProgress, onClick)
		isProgress = true
	} else {
		timeId = setTimeout(() => {
			realCreateNotificationProgress(options.title ?? getAppName(context), content, histroyProgress, onClick)
			timeId = -1
		}, 1000)
	}
}


export function cancelNotificationProgress() : void {
	const context = UTSAndroid.getAppContext() as Context
	const notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
	notificationManager.cancel(DOWNLOAD_PROGRESS_NOTIFICATION_ID)
	reset()
}


function realCreateNotificationProgress(title : string, content : string, progress : number, cb : (() => void) | null) : void {
  setGlobalNotificationProgressCallBack(cb)
	const context = UTSAndroid.getAppContext() as Context
	const notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
	createDownloadChannel(notificationManager)
	const builder = createNotificationBuilder(context)
	builder.setProgress(100, progress.toInt(), false)
	builder.setContentTitle(title)
	builder.setContentText(content)
	builder.setContentIntent(createPendingIntent(context, ACTION_DOWNLOAD_PROGRESS));
	notificationManager.notify(DOWNLOAD_PROGRESS_NOTIFICATION_ID, builder.build())
}


export function finishNotificationProgress(options : FinishNotificationProgressOptions) {
  setGlobalNotificationProgressFinishCallBack(options.onClick)
	const context = UTSAndroid.getAppContext() as Context
	const notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
	createDownloadChannel(notificationManager)
	const builder = createNotificationBuilder(context)
	builder.setProgress(0, 0, false)
	builder.setContentTitle(options.title ?? getAppName(context))
	builder.setContentText(options.content)
	//小米rom setOngoing未false的时候，会被通知管理器归为不重要通知
	// builder.setOngoing(false)
	builder.setAutoCancel(true);
	builder.setContentIntent(createPendingIntent(context, ACTION_DOWNLOAD_FINISH));
	notificationManager.notify(DOWNLOAD_PROGRESS_NOTIFICATION_ID, builder.build())
	reset()
}

function reset() {
	isProgress = false
	notificationBuilder = null
	histroyProgress = 0
	if (timeId != -1) {
		clearTimeout(timeId)
		timeId = -1
	}
}

function createPendingIntent(context : Context, action : string) : PendingIntent {
	const intent = new Intent(action);
	intent.setComponent(new ComponentName(context.getPackageName(), "uts.sdk.modules.utsProgressNotification.TransparentActivity"));
	let flags = PendingIntent.FLAG_UPDATE_CURRENT;
	if (Build.VERSION.SDK_INT >= 23) {
		flags = PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE;
	}
	return PendingIntent.getActivity(context, DOWNLOAD_PROGRESS_NOTIFICATION_ID, intent, flags);
}

function createDownloadChannel(notificationManager : NotificationManager) {
	if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
		const channel = new NotificationChannel(
			DC_DOWNLOAD_CHANNEL_ID,
			DC_DOWNLOAD_CHANNEL_NAME,
			NotificationManager.IMPORTANCE_LOW
		)
		notificationManager.createNotificationChannel(channel)
	}
}
@Suppress("DEPRECATION")
function createNotificationBuilder(context : Context) : Notification.Builder {
	if (notificationBuilder == null) {
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
			notificationBuilder = new Notification.Builder(context, DC_DOWNLOAD_CHANNEL_ID)
		} else {
			notificationBuilder = new Notification.Builder(context)
		}
		notificationBuilder!.setSmallIcon(context.getApplicationInfo().icon)
		notificationBuilder!.setOngoing(true)
		notificationBuilder!.setSound(null)
	}
	return notificationBuilder!
}

@Suppress("DEPRECATION")
function getAppName(context : Context) : string {
	let appName = ""
	try {
		const packageManager = context.getPackageManager()
		const applicationInfo = packageManager.getApplicationInfo(context.getPackageName(), 0)
		appName = packageManager.getApplicationLabel(applicationInfo) as string
	} catch (e : Exception) {
		e.printStackTrace()
	}
	return appName
}

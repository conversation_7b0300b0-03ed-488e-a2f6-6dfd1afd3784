<template>
  <view class="update-check-container">
    <view class="update-item" @click="checkUpdate">
      <view class="update-info">
        <text class="update-title">检查更新</text>
        <text class="update-desc">当前版本: v{{ currentVersion }}</text>
      </view>
      <view class="update-arrow">
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>
  </view>
</template>

<script>
import appUpdate from '@/utils/app-update.js'

export default {
  name: 'AppUpdateCheck',
  data() {
    return {
      currentVersion: '1.0.0'
    }
  },
  mounted() {
    this.getCurrentVersion()
  },
  methods: {
    /**
     * 获取当前版本号
     */
    getCurrentVersion() {
      this.currentVersion = appUpdate.getCurrentVersion()
    },
    
    /**
     * 手动检查更新
     */
    checkUpdate() {
      appUpdate.checkUpdate({
        silent: false,
        showLoading: true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.update-check-container {
  background: #fff;
  border-radius: 12rpx;
  margin: 20rpx;
  overflow: hidden;
}

.update-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: #f8f8f8;
  }
}

.update-info {
  flex: 1;
  
  .update-title {
    display: block;
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 8rpx;
  }
  
  .update-desc {
    display: block;
    font-size: 26rpx;
    color: #999;
  }
}

.update-arrow {
  color: #ccc;
  font-size: 28rpx;
}

/* 如果没有iconfont，使用伪元素创建箭头 */
.update-arrow::after {
  content: '>';
  font-size: 28rpx;
  color: #ccc;
}
</style>

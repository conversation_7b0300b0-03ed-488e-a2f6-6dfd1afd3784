# 支付工具使用文档

## 概述

`utils/paymentUtils.js` 提供了一套统一的支付解决方案，支持微信小程序、APP、H5三端支付，自动适配不同平台，简化支付流程的开发和维护。

## 功能特性

- 🚀 **多平台支持**：微信小程序、APP、H5全平台支持
- 🔧 **统一接口**：一套代码适配所有平台
- 📱 **自动识别**：自动检测当前运行平台
- 🎯 **灵活配置**：支持自定义回调和跳转
- 📢 **订阅消息**：集成微信小程序订阅消息功能
- ⚡ **简化调用**：提供快速调用方法
- 🛡️ **错误处理**：完整的错误处理机制

## 快速开始

### 1. 导入工具类

```javascript
// 默认导入（推荐）
import paymentUtils from '@/utils/paymentUtils.js'
```

### 2. 基础用法

#### 最简单的调用方式

```javascript
// 快速支付，使用默认配置
const paymentData = {
    appId: 'your_app_id',
    nonceStr: 'random_string',
    prepayId: 'prepay_id_from_server',
    timestamp: 1234567890,
    sign: 'payment_sign',
    partnerId: 'partner_id' // APP支付需要
}

try {
    await paymentUtils.quickPay(paymentData)
    console.log('支付成功')
} catch (error) {
    console.error('支付失败:', error)
}
```

#### 完整配置调用

```javascript
await paymentUtils.unifiedPay({
    paymentData: paymentData,
    onSuccess: () => {
        console.log('支付成功回调')
    },
    onFail: (error) => {
        console.error('支付失败回调:', error)
    },
    successRedirectUrl: '/user/order_list?tab=0'
})
```

### 3. 完整配置示例

```javascript
const options = {
    paymentData: {
        appId: 'wx1234567890',
        nonceStr: 'abc123',
        prepayId: 'prepay_id_123',
        timestamp: Date.now(),
        sign: 'signature_string',
        partnerId: 'partner_123'
    },
    // 成功回调
    onSuccess: () => {
        console.log('自定义成功处理')
        // 可以在这里添加自定义逻辑
    },
    // 失败回调
    onFail: (error) => {
        console.error('自定义失败处理:', error)
        // 可以在这里添加自定义错误处理
    },
    // 取消回调
    onCancel: () => {
        console.log('用户取消了支付')
        // 可以在这里添加取消支付的处理逻辑
    },
    // 订阅消息配置
    subscribeConfig: {
        tmplIds: ['template_id_1', 'template_id_2'],
        onSubscribeSuccess: (res) => {
            console.log('订阅成功:', res)
        },
        onSubscribeFail: (err) => {
            console.error('订阅失败:', err)
        }
    },
    // 成功后跳转页面
    successRedirectUrl: '/user/order_list?tab=0'
}

try {
    await paymentUtils.unifiedPay(options)
} catch (error) {
    console.error('支付过程出错:', error)
}
```

## API 参考

### unifiedPay(options)

统一支付方法，支持完整的配置选项。

#### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| options.paymentData | Object | 是 | 支付数据对象 |
| options.onSuccess | Function | 否 | 支付成功回调 |
| options.onFail | Function | 否 | 支付失败回调 |
| options.onCancel | Function | 否 | 支付取消回调 |
| options.subscribeConfig | Object | 否 | 订阅消息配置 |
| options.successRedirectUrl | String | 否 | 成功后跳转页面 |

#### paymentData 对象结构

```javascript
{
    appId: 'wx1234567890',           // 微信应用ID
    nonceStr: 'random_string',       // 随机字符串
    prepayId: 'prepay_id_123',       // 预支付ID
    timestamp: 1234567890,           // 时间戳
    sign: 'payment_signature',       // 支付签名
    partnerId: 'partner_123'         // 商户ID（APP支付必需）
}
```

### quickPay(paymentData, successUrl)

快速支付方法，适用于简单场景。

#### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| paymentData | Object | 是 | 支付数据对象 |
| successUrl | String | 否 | 成功后跳转页面 |

#### 示例

```javascript
const paymentData = {
    appId: 'wx1234567890',
    nonceStr: 'abc123',
    prepayId: 'prepay_id_123',
    timestamp: Date.now(),
    sign: 'signature_string',
    partnerId: 'partner_123'
}

// 使用默认跳转页面
await paymentUtils.quickPay(paymentData)

// 自定义跳转页面
await paymentUtils.quickPay(paymentData, '/user/order_success')
```

### getCurrentPlatform()

获取当前运行平台

#### 返回值

- `'app-plus'` - APP环境
- `'mp-weixin'` - 微信小程序
- `'h5'` - H5环境
- `'unknown'` - 未知环境

## 在现有代码中的应用

### 原有代码改造示例

将 `Cashier.vue` 中的支付逻辑改造为使用工具类：

```javascript
// 原有的 confirmPay 方法改造
import paymentUtils from '@/utils/paymentUtils.js'

async confirmPay() {
    try {
        const res = await this.$api.service.nowPay({
            orderId: this.id,
            couponId: this.confirmCou ? this.confirmCou.couponId : 0,
            type: 1
        })

        if (res.code === '-1') {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
            return
        }

        // 使用支付工具类
        await paymentUtils.unifiedPay({
            paymentData: res.data,
            subscribeConfig: {
                tmplIds: this.tmplIds,
                onSubscribeSuccess: (res) => {
                    console.log('订阅成功:', res)
                }
            },
            successRedirectUrl: '/user/order_list?tab=0'
        })

    } catch (error) {
        console.error('支付失败:', error)
    }
}
```

### 其他应用场景

#### 1. 保证金支付

```javascript
import paymentUtils from '@/utils/paymentUtils.js'

async confirmPay() {
    try {
        const res = await this.$api.shifu.nowPay({
            payPrice: 200,
            couponId: 0,
            type: 1
        })

        if (res.code === '-1') {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
            return
        }

        // 简化调用
        await paymentUtils.quickPay(res.data, '/shifu/index')

    } catch (error) {
        console.error('支付失败:', error)
    }
}
```

#### 2. 活动支付

```javascript
import paymentUtils from '@/utils/paymentUtils.js'

async confirmPay() {
    try {
        const res = await this.$api.service.huodongPay({
            // 支付参数
        })

        if (res.code === '-1') {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
            return
        }

        await paymentUtils.unifiedPay({
            paymentData: res.data,
            successRedirectUrl: '/user/tiaozhuan',
            onSuccess: () => {
                // 支付成功后的特殊处理
                console.log('活动支付成功')
            }
        })

    } catch (error) {
        console.error('支付失败:', error)
    }
}
```

## 平台差异说明

### 微信小程序
- 支持订阅消息功能
- 使用 `uni.requestPayment` 进行支付
- 参数格式：`timeStamp`, `nonceStr`, `package`, `signType`, `paySign`

### APP
- 不支持订阅消息
- 使用 `uni.requestPayment` 配合 `provider: "wxpay"`
- 参数格式：`appid`, `noncestr`, `package`, `partnerid`, `prepayid`, `timestamp`, `sign`

### H5
- 需要在微信浏览器环境中使用
- 使用 `WeixinJSBridge.invoke` 进行支付
- 不支持订阅消息功能

## 错误处理

工具类会自动处理以下错误情况：

1. **用户取消支付**：显示"您已取消支付"提示
2. **支付失败**：显示"支付失败，请稍后重试"提示
3. **平台不支持**：抛出"不支持的平台类型"错误
4. **参数缺失**：抛出"支付数据不能为空"错误

## 注意事项

1. **时间戳格式**：确保传入的 `timestamp` 是数字类型，工具类会自动转换为字符串
2. **APP支付**：APP平台需要提供 `partnerId` 参数
3. **订阅消息**：仅在微信小程序平台有效
4. **H5支付**：需要在微信浏览器环境中使用
5. **错误处理**：建议使用 try-catch 包装支付调用

## 迁移指南

### 替换现有支付代码步骤

1. **导入支付工具类**
2. **保留API调用逻辑**
3. **替换支付处理部分**
4. **移除平台判断代码**
5. **简化错误处理逻辑**

### 迁移前后对比

**迁移前**：
- 需要手动判断平台
- 重复的支付处理代码
- 复杂的错误处理逻辑
- 代码量大，维护困难

**迁移后**：
- 自动平台适配
- 统一的支付接口
- 标准化错误处理
- 代码简洁，易于维护

## 更新日志

### v1.0.0
- 初始版本发布
- 支持微信小程序、APP、H5三端支付
- 集成订阅消息功能
- 提供统一的错误处理机制
- 支持自定义回调和页面跳转
